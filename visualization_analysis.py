import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
import matplotlib.font_manager as fm

# 清除matplotlib缓存
plt.rcParams.clear()

# 查找并设置中文字体
print("正在设置中文字体...")
chinese_fonts = []
for font in fm.fontManager.ttflist:
    if any(keyword in font.name for keyword in ['YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']):
        chinese_fonts.append(font.name)

if chinese_fonts:
    font_name = chinese_fonts[0]  # 使用第一个找到的中文字体
    print(f"使用字体: {font_name}")
    plt.rcParams['font.sans-serif'] = [font_name]
else:
    print("使用备用字体设置")
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']

plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

# 设置图表样式
try:
    plt.style.use('seaborn-v0_8')
except:
    try:
        plt.style.use('seaborn')
    except:
        pass  # 使用默认样式

sns.set_palette("husl")

def load_data():
    """加载数据集"""
    data_dir = '数据可视化数据集-A'

    # 加载二手房数据
    house_data = pd.read_excel(f'{data_dir}/二手房数据.xlsx')

    # 加载餐厅消费数据
    restaurant_data = pd.read_excel(f'{data_dir}/某餐厅顾客消费记录.xlsx')

    # 加载地铁站数据
    subway_data = pd.read_excel(f'{data_dir}/2022年北京市工作日早高峰出站量前20的地铁站.xlsx')

    return house_data, restaurant_data, subway_data

def create_house_visualizations(house_data):
    """创建二手房数据可视化"""

    # 确保中文字体设置
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False

    # 1. 散点图：面积 vs 总价关系分析
    plt.figure(figsize=(12, 8))

    # 按区域分组绘制散点图
    districts = house_data['所在区'].unique()
    colors = plt.cm.Set3(np.linspace(0, 1, len(districts)))

    for i, district in enumerate(districts):
        district_data = house_data[house_data['所在区'] == district]
        plt.scatter(district_data['面积（平方米）'], district_data['总价（万元）'],
                   alpha=0.6, s=50, color=colors[i], label=district)

    plt.xlabel('面积（平方米）', fontsize=14)
    plt.ylabel('总价（万元）', fontsize=14)
    plt.title('北京二手房面积与总价关系分析', fontsize=16, fontweight='bold')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)

    # 添加趋势线
    z = np.polyfit(house_data['面积（平方米）'], house_data['总价（万元）'], 1)
    p = np.poly1d(z)
    plt.plot(house_data['面积（平方米）'], p(house_data['面积（平方米）']),
             "r--", alpha=0.8, linewidth=2, label='趋势线')

    plt.tight_layout()
    plt.savefig('图表1_二手房面积总价散点图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 箱形图：各区域房价分布对比
    plt.figure(figsize=(14, 8))

    # 计算各区域的房屋数量，只显示数量较多的区域
    district_counts = house_data['所在区'].value_counts()
    top_districts = district_counts.head(10).index
    filtered_data = house_data[house_data['所在区'].isin(top_districts)]

    box_plot = sns.boxplot(data=filtered_data, x='所在区', y='单价（元/平方米）',
                          palette='Set2')

    plt.xlabel('所在区', fontsize=14)
    plt.ylabel('单价（元/平方米）', fontsize=14)
    plt.title('北京各区域二手房单价分布对比', fontsize=16, fontweight='bold')
    plt.xticks(rotation=45)

    # 添加均值点
    means = filtered_data.groupby('所在区')['单价（元/平方米）'].mean()
    for i, district in enumerate(top_districts):
        plt.plot(i, means[district], 'ro', markersize=8)

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('图表2_各区域房价箱形图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 热力图：房龄与单价的相关性分析
    plt.figure(figsize=(12, 8))

    # 创建房龄区间
    house_data['房龄区间'] = pd.cut(house_data['房龄（年）'],
                                bins=[0, 5, 10, 15, 20, 25, 50],
                                labels=['0-5年', '6-10年', '11-15年', '16-20年', '21-25年', '25年以上'])

    # 创建面积区间
    house_data['面积区间'] = pd.cut(house_data['面积（平方米）'],
                                bins=[0, 60, 90, 120, 150, 300],
                                labels=['小户型(<60㎡)', '中小户型(60-90㎡)', '中户型(90-120㎡)',
                                       '大户型(120-150㎡)', '超大户型(>150㎡)'])

    # 计算平均单价
    heatmap_data = house_data.groupby(['房龄区间', '面积区间'])['单价（元/平方米）'].mean().unstack()

    sns.heatmap(heatmap_data, annot=True, fmt='.0f', cmap='YlOrRd',
                cbar_kws={'label': '平均单价（元/平方米）'})

    plt.xlabel('面积区间', fontsize=14)
    plt.ylabel('房龄区间', fontsize=14)
    plt.title('房龄与面积对单价影响的热力图分析', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('图表3_房龄面积单价热力图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_restaurant_visualizations(restaurant_data):
    """创建餐厅消费数据可视化"""

    # 确保中文字体设置
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False

    # 4. 小提琴图：不同分店消费金额分布
    plt.figure(figsize=(12, 8))

    violin_plot = sns.violinplot(data=restaurant_data, x='分店', y='消费金额（元）',
                                palette='viridis', inner='box')

    plt.xlabel('分店', fontsize=14)
    plt.ylabel('消费金额（元）', fontsize=14)
    plt.title('各分店顾客消费金额分布分析', fontsize=16, fontweight='bold')

    # 添加统计信息
    for i, store in enumerate(restaurant_data['分店'].unique()):
        store_data = restaurant_data[restaurant_data['分店'] == store]['消费金额（元）']
        median_val = store_data.median()
        plt.text(i, median_val + 50, f'中位数: {median_val:.1f}',
                ha='center', va='bottom', fontweight='bold')

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('图表4_分店消费金额小提琴图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 5. 气泡图：消费金额 vs 满意度（按性别分组）
    plt.figure(figsize=(12, 8))

    # 按性别和顾客类型分组
    for gender in restaurant_data['性别'].unique():
        for customer_type in restaurant_data['顾客类型'].unique():
            subset = restaurant_data[(restaurant_data['性别'] == gender) &
                                   (restaurant_data['顾客类型'] == customer_type)]

            if len(subset) > 0:
                # 气泡大小表示该组合的人数
                bubble_size = len(subset) * 2

                plt.scatter(subset['消费金额（元）'], subset['顾客满意度'],
                           s=bubble_size, alpha=0.6,
                           label=f'{gender}-{customer_type}')

    plt.xlabel('消费金额（元）', fontsize=14)
    plt.ylabel('顾客满意度', fontsize=14)
    plt.title('消费金额与顾客满意度关系分析（气泡大小表示人数）', fontsize=16, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 添加相关性分析
    correlation = restaurant_data['消费金额（元）'].corr(restaurant_data['顾客满意度'])
    plt.text(0.02, 0.98, f'相关系数: {correlation:.3f}',
             transform=plt.gca().transAxes, fontsize=12,
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig('图表5_消费满意度气泡图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 6. 环形图：顾客类型占比分析
    plt.figure(figsize=(10, 8))

    # 计算顾客类型占比
    customer_type_counts = restaurant_data['顾客类型'].value_counts()

    # 创建环形图
    colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']
    wedges, texts, autotexts = plt.pie(customer_type_counts.values,
                                      labels=customer_type_counts.index,
                                      autopct='%1.1f%%',
                                      colors=colors,
                                      startangle=90,
                                      pctdistance=0.85)

    # 创建中心空白区域形成环形图
    centre_circle = plt.Circle((0,0), 0.70, fc='white')
    fig = plt.gcf()
    fig.gca().add_artist(centre_circle)

    # 在中心添加总数信息
    plt.text(0, 0, f'总顾客数\n{len(restaurant_data)}',
             ha='center', va='center', fontsize=16, fontweight='bold')

    plt.title('餐厅顾客类型分布', fontsize=16, fontweight='bold', pad=20)

    # 美化文字
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(12)

    plt.axis('equal')
    plt.tight_layout()
    plt.savefig('图表6_顾客类型环形图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_subway_visualizations(subway_data):
    """创建地铁站数据可视化"""

    # 确保中文字体设置
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False

    # 7. 地理热力图：地铁站出站量地理分布
    fig = go.Figure()

    # 创建散点图表示地铁站位置，颜色表示出站量
    fig.add_trace(go.Scattermapbox(
        lat=subway_data['纬度'],
        lon=subway_data['经度'],
        mode='markers',
        marker=dict(
            size=subway_data['出站量（万人次）'] * 10,  # 根据出站量调整点的大小
            color=subway_data['出站量（万人次）'],
            colorscale='Reds',
            showscale=True,
            colorbar=dict(title="出站量（万人次）")
        ),
        text=subway_data['地铁站'] + '<br>出站量: ' + subway_data['出站量（万人次）'].astype(str) + '万人次',
        hovertemplate='<b>%{text}</b><extra></extra>'
    ))

    fig.update_layout(
        title='北京市地铁站早高峰出站量地理分布热力图',
        mapbox=dict(
            style="open-street-map",
            center=dict(lat=39.9, lon=116.4),
            zoom=10
        ),
        height=600,
        font=dict(family="Microsoft YaHei", size=12)
    )

    fig.write_html('图表7_地铁站地理热力图.html')
    fig.show()

    # 8. 条形图：TOP20地铁站出站量排名
    plt.figure(figsize=(14, 10))

    # 按出站量排序
    sorted_data = subway_data.sort_values('出站量（万人次）', ascending=True)

    # 创建水平条形图
    bars = plt.barh(range(len(sorted_data)), sorted_data['出站量（万人次）'],
                    color=plt.cm.viridis(np.linspace(0, 1, len(sorted_data))))

    plt.yticks(range(len(sorted_data)), sorted_data['地铁站'])
    plt.xlabel('出站量（万人次）', fontsize=14)
    plt.ylabel('地铁站', fontsize=14)
    plt.title('北京市TOP20地铁站早高峰出站量排名', fontsize=16, fontweight='bold')

    # 在条形图上添加数值标签
    for i, (bar, value) in enumerate(zip(bars, sorted_data['出站量（万人次）'])):
        plt.text(value + 0.02, bar.get_y() + bar.get_height()/2,
                f'{value:.2f}', va='center', fontweight='bold')

    plt.grid(True, alpha=0.3, axis='x')
    plt.tight_layout()
    plt.savefig('图表8_地铁站出站量条形图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 9. 华夫饼图：出站量等级分布
    plt.figure(figsize=(12, 8))

    # 将出站量分为不同等级
    subway_data['出站量等级'] = pd.cut(subway_data['出站量（万人次）'],
                                  bins=[0, 1, 1.5, 2, 3],
                                  labels=['低流量(<1万)', '中等流量(1-1.5万)',
                                         '高流量(1.5-2万)', '超高流量(>2万)'])

    level_counts = subway_data['出站量等级'].value_counts()

    # 创建华夫饼图数据
    total_squares = 100
    waffle_data = {}
    cumulative = 0

    colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']

    for i, (level, count) in enumerate(level_counts.items()):
        squares = int(count / len(subway_data) * total_squares)
        waffle_data[level] = squares

    # 绘制华夫饼图
    fig, ax = plt.subplots(figsize=(12, 8))

    # 创建10x10的网格
    square_size = 1
    x_positions = []
    y_positions = []
    colors_list = []

    current_square = 0
    color_idx = 0

    for level, squares in waffle_data.items():
        for _ in range(squares):
            x = current_square % 10
            y = current_square // 10
            x_positions.append(x)
            y_positions.append(y)
            colors_list.append(colors[color_idx])
            current_square += 1
        color_idx += 1

    # 绘制方块
    for x, y, color in zip(x_positions, y_positions, colors_list):
        rect = plt.Rectangle((x, y), square_size, square_size,
                           facecolor=color, edgecolor='white', linewidth=2)
        ax.add_patch(rect)

    # 设置图表属性
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    ax.set_aspect('equal')
    ax.axis('off')

    # 添加图例
    legend_elements = [plt.Rectangle((0,0),1,1, facecolor=colors[i],
                                   label=f'{level}: {count}站')
                      for i, (level, count) in enumerate(level_counts.items())]
    ax.legend(handles=legend_elements, loc='center left', bbox_to_anchor=(1, 0.5))

    plt.title('地铁站出站量等级分布华夫饼图\n(每个方块代表1%)',
              fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.savefig('图表9_出站量等级华夫饼图.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("正在加载数据...")
    house_data, restaurant_data, subway_data = load_data()

    print("正在生成二手房数据可视化...")
    create_house_visualizations(house_data)

    print("正在生成餐厅消费数据可视化...")
    create_restaurant_visualizations(restaurant_data)

    print("正在生成地铁站数据可视化...")
    create_subway_visualizations(subway_data)

    print("所有可视化图表生成完成！")

if __name__ == "__main__":
    main()
