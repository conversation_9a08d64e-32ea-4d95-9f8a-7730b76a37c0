import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import pandas as pd
import numpy as np

# 清除字体缓存
plt.rcParams.clear()

# 查找并设置中文字体
print("正在查找可用的中文字体...")
chinese_fonts = []
for font in fm.fontManager.ttflist:
    if any(keyword in font.name for keyword in ['YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']):
        chinese_fonts.append(font.name)
        print(f"找到字体: {font.name}")

if chinese_fonts:
    font_name = chinese_fonts[0]
    print(f"使用字体: {font_name}")
    plt.rcParams['font.sans-serif'] = [font_name]
else:
    print("未找到中文字体，尝试使用系统字体")
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']

plt.rcParams['axes.unicode_minus'] = False

# 测试中文显示
fig, ax = plt.subplots(figsize=(10, 6))

# 创建测试数据
categories = ['北京', '上海', '广州', '深圳', '杭州']
values = [100, 85, 75, 90, 80]

bars = ax.bar(categories, values, color=['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc'])

# 设置标题和标签
ax.set_title('中文字体测试图表', fontsize=16, fontweight='bold')
ax.set_xlabel('城市', fontsize=14)
ax.set_ylabel('数值', fontsize=14)

# 在柱子上添加数值标签
for bar, value in zip(bars, values):
    height = bar.get_height()
    ax.text(bar.get_x() + bar.get_width()/2., height + 1,
            f'{value}', ha='center', va='bottom', fontweight='bold')

plt.tight_layout()
plt.savefig('中文字体测试.png', dpi=300, bbox_inches='tight')
plt.show()

print("字体测试完成！如果图表中的中文正常显示，说明字体设置成功。")
